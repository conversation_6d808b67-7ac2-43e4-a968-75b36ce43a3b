/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.header-controls {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateX(-50px) translateY(-50px); }
    100% { transform: translateX(50px) translateY(50px); }
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

.header-content .subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
}

.unit-badge {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    position: relative;
    z-index: 1;
}

.unit-badge span {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    display: block;
}

.badge-text {
    font-size: 1rem;
    color: #666;
    margin-top: 5px;
}

/* Vietnamese Toggle Styles */
.vietnamese-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.9);
    padding: 10px 15px;
    border-radius: 25px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .toggle-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* Vietnamese text visibility */
.vietnamese-text {
    transition: all 0.3s ease;
    opacity: 1;
}

.vietnamese-hidden .vietnamese-text {
    display: none !important;
}

.vietnamese-hidden .job-title-vietnamese {
    display: none !important;
}

/* Toggle switch hover effects */
.toggle-switch:hover .toggle-slider {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.vietnamese-toggle:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

/* Navigation Styles */
.navigation {
    margin-bottom: 30px;
}

.nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.nav-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #333;
}

.nav-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.nav-btn.active {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.nav-btn i {
    font-size: 1.5rem;
    color: #666;
}

.nav-btn span {
    font-size: 0.9rem;
}

/* Activity Styles */
.activity {
    display: none;
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.activity.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.activity-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid #f0f0f0;
}

.activity-header h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 10px;
}

.activity-header h2 i {
    color: #667eea;
    margin-right: 10px;
}

.activity-description {
    font-size: 1.1rem;
    color: #666;
    font-weight: 300;
}

/* Job Cards Styles */
.job-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.job-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.job-card:hover {
    transform: translateY(-5px);
}

.job-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.job-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.job-title-vietnamese {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 15px;
    font-style: italic;
}

.job-details {
    position: relative;
    z-index: 1;
}

.job-section {
    margin-bottom: 15px;
}

.job-section h4 {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.job-section p {
    font-size: 1rem;
    line-height: 1.4;
}

/* Word Box Styles */
.word-box {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.word-box h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.word-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.word-tag {
    background: rgba(255,255,255,0.8);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #333;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.word-tag:hover {
    border-color: #667eea;
    background: white;
    transform: translateY(-2px);
}

/* Button Styles */
.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

/* Crossword Styles */
.crossword-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.crossword-grid {
    display: grid;
    grid-template-columns: repeat(16, 30px);
    grid-template-rows: repeat(16, 30px);
    gap: 1px;
    background: #333;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.crossword-cell {
    width: 30px;
    height: 30px;
    background: white;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    position: relative;
}

.crossword-cell.black {
    background: #333;
}

.crossword-cell.numbered::before {
    content: attr(data-number);
    position: absolute;
    top: 1px;
    left: 2px;
    font-size: 8px;
    color: #666;
}

.crossword-cell input {
    width: 100%;
    height: 100%;
    border: none;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    background: transparent;
}

.crossword-cell input:focus {
    outline: 2px solid #667eea;
    background: #f0f8ff;
}

.crossword-clues {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    width: 100%;
    max-width: 600px;
}

.clues-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.clues-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
    text-align: center;
}

.clue-item {
    margin-bottom: 10px;
    padding: 8px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.clue-number {
    font-weight: bold;
    color: #667eea;
}

/* Game Styles */
.game-container {
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.game-board {
    display: grid;
    gap: 15px;
    margin: 20px 0;
}

.game-question {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.5;
}

.game-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.option-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border: none;
    padding: 15px 20px;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.option-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.option-btn.correct {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.option-btn.incorrect {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #333;
}

.score-display {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 20px 0;
}

/* Application Form Styles */
.application-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.application-template {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.application-template h4 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.application-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-section label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-section textarea {
    padding: 12px;
    border: 2px solid rgba(255,255,255,0.5);
    border-radius: 10px;
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    resize: vertical;
    min-height: 80px;
    background: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.form-section textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
}

.pair-work-tips {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 30px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.pair-work-tips h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.pair-work-tips ul {
    list-style: none;
    padding: 0;
}

.pair-work-tips li {
    margin-bottom: 10px;
    padding: 8px 12px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Interview Styles */
.role-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.role-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.role-card h4 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.role-card p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.interview-content {
    margin: 30px 0;
    min-height: 200px;
}

.question-cards, .response-guide, .job-interview {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.question-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    font-weight: 600;
    color: #333;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.question-card:hover {
    transform: translateY(-3px);
}

.response-tips {
    display: grid;
    gap: 15px;
    margin-top: 20px;
}

.tip-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.tip-card h5 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.interview-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

.questions-column, .responses-column {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    padding: 20px;
    border-radius: 12px;
}

.questions-column h5, .responses-column h5 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.1rem;
}

.question-item, .response-item {
    margin-bottom: 12px;
    padding: 10px;
    background: rgba(255,255,255,0.8);
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.scenario-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    margin-top: 15px;
}

/* Enhanced Interview Example Styles */
.interview-instructions {
    text-align: center;
    margin: 20px 0;
    font-style: italic;
    color: #666;
}

.interview-questions-list {
    display: grid;
    gap: 15px;
    margin: 25px 0;
}

.interview-question-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.interview-question-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.question-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.question-number {
    background: rgba(255,255,255,0.9);
    color: #333;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.question-text {
    flex: 1;
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.question-header i {
    color: #666;
    transition: transform 0.3s ease;
}

.interview-question-card:hover .question-header i {
    transform: translateX(5px);
}

.example-answers-container {
    margin-top: 30px;
}

.example-answers {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.example-instructions {
    text-align: center;
    margin-bottom: 25px;
    color: #666;
    font-style: italic;
}

.answer-examples {
    display: grid;
    gap: 20px;
    margin: 25px 0;
}

.answer-card {
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.answer-card:hover {
    transform: translateY(-2px);
}

.bad-answer {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-left: 5px solid #e74c3c;
}

.okay-answer {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-left: 5px solid #f39c12;
}

.good-answer {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-left: 5px solid #27ae60;
}

.answer-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.answer-header i {
    font-size: 1.2rem;
}

.bad-answer .answer-header i {
    color: #e74c3c;
}

.okay-answer .answer-header i {
    color: #f39c12;
}

.good-answer .answer-header i {
    color: #27ae60;
}

.answer-label {
    font-weight: bold;
    font-size: 1.1rem;
    color: #333;
}

.answer-content {
    background: rgba(255,255,255,0.9);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-style: italic;
    line-height: 1.5;
    color: #333;
}

.answer-feedback {
    font-size: 0.9rem;
    color: #555;
    line-height: 1.4;
}

.answer-feedback strong {
    color: #333;
}

.practice-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin-top: 25px;
}

.practice-section h6 {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.practice-section p {
    margin-bottom: 20px;
    opacity: 0.9;
}

/* Logic Puzzle Styles */
.logic-puzzle-board {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.job-seekers-section, .job-ads-section {
    background: rgba(255,255,255,0.9);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.job-seekers-section h4, .job-ads-section h4 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.seekers-container, .ads-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.seeker-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    padding: 15px;
    border-radius: 12px;
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.seeker-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.seeker-card:active {
    cursor: grabbing;
}

.seeker-card h5 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.seeker-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

.job-ad-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.job-ad-card h5 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.job-ad-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 8px;
}

.drop-zone {
    background: rgba(255,255,255,0.8);
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin-top: 10px;
    transition: all 0.3s ease;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.drop-zone.drag-over {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.drop-zone.has-assignment {
    background: rgba(102, 126, 234, 0.2);
    border-color: #667eea;
    border-style: solid;
}

.drop-zone.correct-match {
    background: rgba(40, 167, 69, 0.2);
    border-color: #28a745;
    color: #155724;
}

.drop-zone.incorrect-match {
    background: rgba(220, 53, 69, 0.2);
    border-color: #dc3545;
    color: #721c24;
}

.logic-clues {
    background: linear-gradient(135d, #d299c2 0%, #fef9d7 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 30px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.logic-clues h4 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.2rem;
}

.clues-list {
    display: grid;
    gap: 10px;
}

/* Scenarios Game Styles */
.scenario-game {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.scenario-progress {
    text-align: center;
    margin-bottom: 30px;
}

.scenario-progress span {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #e0e0e0;
    border-radius: 5px;
    margin: 15px 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 5px;
    transition: width 0.5s ease;
    width: 0%;
}

.scenario-text {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.scenario-text h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.scenario-text p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #444;
    font-style: italic;
    margin: 0;
}

.scenario-questions {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.question-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.question-section h5 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: center;
}

.scenario-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.scenario-score {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    border-radius: 10px;
}

.results-screen {
    text-align: center;
    padding: 30px;
}

.results-screen h4 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.final-score {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.final-score p {
    margin: 5px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Family Feud Styles */
.feud-game {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feud-header {
    margin-bottom: 30px;
}

.team-scores {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
    text-align: center;
}

.team-score {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.team-score h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.team-score span {
    font-size: 2rem;
    font-weight: 700;
}

.current-round {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 15px;
    border-radius: 12px;
    color: #333;
    font-weight: 600;
}

.strikes {
    margin-top: 10px;
    font-size: 0.9rem;
}

.feud-board {
    margin: 30px 0;
}

.feud-question {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feud-question h4 {
    color: #333;
    font-size: 1.4rem;
    margin: 0;
}

.feud-answers {
    display: grid;
    gap: 10px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.feud-answer {
    display: grid;
    grid-template-columns: 50px 1fr auto;
    align-items: center;
    background: #333;
    color: white;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.feud-answer.revealed {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    transform: scale(1.02);
}

.answer-number {
    background: rgba(255,255,255,0.2);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.answer-text {
    padding: 0 15px;
}

.answer-points {
    background: rgba(255,255,255,0.2);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
}

.feud-options {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.feud-options h5 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.answer-choices {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.answer-choices .option-btn.selected {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.feud-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.feud-results {
    text-align: center;
    padding: 30px;
}

.feud-results h4 {
    color: #333;
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.final-scores {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.final-scores .final-score {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.final-scores .final-score h5 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.final-scores .final-score span {
    font-size: 1.8rem;
    font-weight: 700;
}

.winner-announcement {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.winner-announcement h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

/* Jeopardy Styles */
.jeopardy-game {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.jeopardy-score {
    text-align: center;
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 30px;
    padding: 15px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.jeopardy-board {
    display: grid;
    gap: 2px;
    background: #333;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.jeopardy-categories {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
}

.jeopardy-category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 700;
    font-size: 1rem;
    border-radius: 5px;
}

.jeopardy-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
}

.jeopardy-cell {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 5px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.jeopardy-cell:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.05);
}

.jeopardy-cell.answered {
    background: #666;
    cursor: default;
    opacity: 0.5;
}

.jeopardy-cell.answered:hover {
    transform: none;
}

.jeopardy-question {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.question-display {
    text-align: center;
}

.question-category {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 25px;
}

.question-text {
    font-size: 1.4rem;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.5;
    font-weight: 500;
}

.question-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.jeopardy-controls {
    text-align: center;
}

.jeopardy-results {
    text-align: center;
    padding: 30px;
}

.jeopardy-results h4 {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.final-jeopardy-score {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.final-jeopardy-score p {
    margin: 8px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.performance-message {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 20px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .header-controls {
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }

    .vietnamese-toggle {
        order: 1;
    }

    .unit-badge {
        order: 2;
    }

    .toggle-label {
        font-size: 0.8rem;
    }

    .jeopardy-categories,
    .jeopardy-row {
        grid-template-columns: repeat(2, 1fr);
    }

    .jeopardy-category-header {
        font-size: 0.9rem;
        padding: 12px;
    }

    .jeopardy-cell {
        font-size: 1.2rem;
        padding: 15px;
        min-height: 50px;
    }

    .question-text {
        font-size: 1.2rem;
    }

    .question-options {
        grid-template-columns: 1fr;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .nav-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .job-cards-container {
        grid-template-columns: 1fr;
    }

    .crossword-grid {
        grid-template-columns: repeat(16, 25px);
        grid-template-rows: repeat(16, 25px);
    }

    .crossword-cell {
        width: 25px;
        height: 25px;
        font-size: 12px;
    }

    .crossword-clues {
        grid-template-columns: 1fr;
    }

    .game-options {
        grid-template-columns: 1fr;
    }
}
